{
  "name": "vite_react_shadcn_ts",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "build:dev": "vite build --mode development",
    "lint": "eslint .",
    "preview": "vite preview",
    "update-types": "npx supabase gen types typescript --project-id qsldppxjmrplbmukqorj > src/lib/database.types.ts && npx supabase gen types typescript --project-id qsldppxjmrplbmukqorj > src/integrations/supabase/types.ts",
    "test": "vitest",
    "test:e2e": "playwright test --config=playwright-mcp.config.ts",
    "test:e2e:headed": "playwright test --config=playwright-mcp.config.ts --headed",
    "test:e2e:auth": "playwright test --config=playwright-mcp.config.ts tests/e2e/auth --headed",
    "test:e2e:clubs": "playwright test --config=playwright-mcp.config.ts tests/e2e/book-clubs --headed",
    "test:e2e:profile": "playwright test --config=playwright-mcp.config.ts tests/e2e/profile --headed",
    "test:e2e:books": "playwright test --config=playwright-mcp.config.ts tests/e2e/books --headed",
    "test:e2e:events": "playwright test --config=playwright-mcp.config.ts tests/e2e/events --headed",
    "test:refactoring": "playwright test --config=tests/e2e/playwright.config.ts tests/e2e/tests/",
    "test:refactoring:headed": "playwright test --config=tests/e2e/playwright.config.ts tests/e2e/tests/ --headed",
    "test:refactoring:debug": "playwright test --config=tests/e2e/playwright.config.ts tests/e2e/tests/ --debug",
    "test:refactoring:report": "playwright show-report",
    "test:refactoring:run": "npx ts-node tests/e2e/run-tests.ts",
    "test:compatibility": "playwright test --config=tests/e2e/playwright.config.ts tests/e2e/tests/backward-compatibility.spec.ts",
    "test:books-section": "playwright test --config=tests/e2e/playwright.config.ts tests/e2e/tests/books-section-functionality.spec.ts",
    "test:personal-books": "playwright test --config=tests/e2e/playwright.config.ts tests/e2e/tests/personal-books-service.spec.ts",
    "test:store-requests": "playwright test --config=tests/e2e/playwright.config.ts tests/e2e/tests/store-requests-service.spec.ts",
    "test:integration": "playwright test --config=tests/e2e/playwright.config.ts tests/e2e/tests/cross-module-integration.spec.ts",
    "test:e2e:admin": "playwright test --config=playwright-mcp.config.ts tests/e2e/admin --headed",
    "test:e2e:mobile": "playwright test --config=playwright-mcp.config.ts tests/e2e/mobile --headed",
    "test:e2e:accessibility": "playwright test --config=playwright-mcp.config.ts tests/e2e/accessibility --headed",
    "test:e2e:workflows": "playwright test --config=playwright-mcp.config.ts tests/e2e/workflows --headed",
    "test:e2e:debug": "playwright test --config=playwright-mcp.config.ts --debug",
    "test:e2e:report": "playwright show-report mcp-report",
    "test:e2e:install": "playwright install"
  },
  "dependencies": {
    "@hello-pangea/dnd": "^18.0.1",
    "@hookform/resolvers": "^3.9.0",
    "@radix-ui/react-accordion": "^1.2.0",
    "@radix-ui/react-alert-dialog": "^1.1.1",
    "@radix-ui/react-aspect-ratio": "^1.1.0",
    "@radix-ui/react-avatar": "^1.1.0",
    "@radix-ui/react-checkbox": "^1.1.1",
    "@radix-ui/react-collapsible": "^1.1.0",
    "@radix-ui/react-context-menu": "^2.2.1",
    "@radix-ui/react-dialog": "^1.1.2",
    "@radix-ui/react-dropdown-menu": "^2.1.1",
    "@radix-ui/react-hover-card": "^1.1.1",
    "@radix-ui/react-label": "^2.1.0",
    "@radix-ui/react-menubar": "^1.1.1",
    "@radix-ui/react-navigation-menu": "^1.2.0",
    "@radix-ui/react-popover": "^1.1.1",
    "@radix-ui/react-progress": "^1.1.0",
    "@radix-ui/react-radio-group": "^1.2.0",
    "@radix-ui/react-scroll-area": "^1.1.0",
    "@radix-ui/react-select": "^2.1.1",
    "@radix-ui/react-separator": "^1.1.0",
    "@radix-ui/react-slider": "^1.2.0",
    "@radix-ui/react-slot": "^1.1.0",
    "@radix-ui/react-switch": "^1.1.0",
    "@radix-ui/react-tabs": "^1.1.0",
    "@radix-ui/react-toast": "^1.2.1",
    "@radix-ui/react-toggle": "^1.1.0",
    "@radix-ui/react-toggle-group": "^1.1.0",
    "@radix-ui/react-tooltip": "^1.1.4",
    "@sentry/react": "^9.10.1",
    "@supabase/supabase-js": "^2.49.4",
    "@tanstack/react-query": "^5.56.2",
    "@types/uuid": "^9.0.8",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "cmdk": "^1.0.0",
    "date-fns": "^3.6.0",
    "embla-carousel-autoplay": "^8.6.0",
    "embla-carousel-react": "^8.3.0",
    "emoji-picker-react": "^4.12.2",
    "input-otp": "^1.2.4",
    "lucide-react": "^0.462.0",
    "next-themes": "^0.3.0",
    "puppeteer": "^24.9.0",
    "react": "^18.3.1",
    "react-day-picker": "^8.10.1",
    "react-dom": "^18.3.1",
    "react-dropzone": "^14.3.8",
    "react-helmet-async": "^2.0.5",
    "react-hook-form": "^7.53.0",
    "react-resizable-panels": "^2.1.3",
    "react-router-dom": "^6.26.2",
    "recharts": "^2.12.7",
    "sonner": "^1.5.0",
    "tailwind-merge": "^2.5.2",
    "tailwindcss-animate": "^1.0.7",
    "uuid": "^9.0.1",
    "vaul": "^0.9.3",
    "zod": "^3.23.8"
  },
  "devDependencies": {
    "@eslint/js": "^9.9.0",
<<<<<<< HEAD
    "@modelcontextprotocol/server-puppeteer": "^2025.5.12",
=======
    "@playwright/test": "^1.53.1",
>>>>>>> e71d473d7c81018f668ee5bc5bf8e6f8e1ff16bf
    "@tailwindcss/typography": "^0.5.15",
    "@testing-library/dom": "^10.4.0",
    "@testing-library/jest-dom": "^6.6.3",
    "@testing-library/react": "^16.3.0",
    "@testing-library/react-hooks": "^8.0.1",
    "@testing-library/user-event": "^14.6.1",
    "@types/node": "^22.5.5",
    "@types/react": "^18.3.3",
    "@types/react-dom": "^18.3.0",
    "@vitejs/plugin-react-swc": "^3.5.0",
    "autoprefixer": "^10.4.20",
    "eslint": "^9.9.0",
    "eslint-plugin-react-hooks": "^5.1.0-rc.0",
    "eslint-plugin-react-refresh": "^0.4.9",
    "globals": "^15.9.0",
    "jsdom": "^26.1.0",
    "lovable-tagger": "^1.1.7",
    "node-mocks-http": "^1.17.2",
    "postcss": "^8.4.47",
    "supabase": "^2.20.12",
    "tailwind-scrollbar": "^3.0.0",
    "tailwindcss": "^3.4.11",
    "typescript": "^5.5.3",
    "typescript-eslint": "^8.0.1",
    "vite": "^5.4.1",
    "vitest": "^3.1.3"
  }
}
